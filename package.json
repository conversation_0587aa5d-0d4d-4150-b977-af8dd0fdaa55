{"name": "@browsermcp/mcp", "version": "0.1.3", "description": "MCP server for browser automation using Browser MCP", "author": "Browser MCP", "homepage": "https://browsermcp.io", "bugs": "https://github.com/browsermcp/mcp/issues", "type": "module", "bin": {"mcp-server-browsermcp": "dist/index.js"}, "files": ["dist"], "scripts": {"typecheck": "tsc --noEmit", "build": "tsup src/index.ts --format esm && shx chmod +x dist/*.js", "prepare": "npm run build", "watch": "tsup src/index.ts --format esm --watch ", "inspector": "CLIENT_PORT=9001 SERVER_PORT=9002 pnpx @modelcontextprotocol/inspector node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.8.0", "commander": "^13.1.0", "ws": "^8.18.1", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.3"}, "devDependencies": {"@r2r/messaging": "workspace:*", "@repo/config": "workspace:*", "@repo/messaging": "workspace:*", "@repo/types": "workspace:*", "@repo/utils": "workspace:*", "@types/ws": "^8.18.0", "shx": "^0.3.4", "tsup": "^8.4.0", "typescript": "^5.6.2"}}